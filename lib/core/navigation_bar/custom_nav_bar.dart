// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';

class CustomNavBar extends StatelessWidget {
  final NavBarConfig navBarConfig;
  final NavBarDecoration navBarDecoration;

  const CustomNavBar({
    super.key,
    required this.navBarConfig,
    this.navBarDecoration = const NavBarDecoration(),
  });

  Widget _buildItem(ItemConfig item, bool isSelected, BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      constraints: const BoxConstraints(
        minHeight: 65,
        maxHeight: 85,
      ),
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // Icon with enhanced styling and heartbeat animation
          _HeartbeatIcon(
            isSelected: isSelected,
            icon: item.icon,
            primaryColor: theme.colorScheme.primary,
            isDark: isDark,
          ),

          const SizedBox(height: 4),

          // Text with better typography and animation
          Expanded(
            child: Container(
              alignment: Alignment.center,
              child: AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: AppTextStyles.font12Regular.copyWith(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : (isDark ? Colors.grey[400] : Colors.grey[600]),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  fontSize: isSelected ? 11 : 10,
                  height: 1.1,
                  letterSpacing: -0.2,
                ),
                child: Text(
                  item.title!,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  softWrap: false,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return DecoratedNavBar(
      decoration: navBarDecoration,
      height: navBarConfig.navBarHeight,
      child: Container(
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: isDark ? Colors.grey[800]! : Colors.grey[200]!,
              width: 0.5,
            ),
          ),
        ),
        child: SafeArea(
          minimum: const EdgeInsets.only(bottom: 2),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 80,
              maxHeight: 95,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: navBarConfig.items.map((item) {
                int index = navBarConfig.items.indexOf(item);
                return Expanded(
                  child: InkWell(
                    onTap: () async {
                      try {
                        // Check if context is still valid
                        if (!context.mounted) {
                          debugPrint(
                              'Context not mounted, skipping navigation');
                          return;
                        }

                        debugPrint(
                            'Navigation attempt: index=$index, mounted=${context.mounted}');

                        // Play sound first (safer operation)
                        try {
                          SoundManager.playClickSound();
                        } catch (soundError) {
                          debugPrint('Sound error: $soundError');
                        }

                        // Add a small delay to ensure context stability
                        await Future.delayed(const Duration(milliseconds: 50));

                        // Double-check context is still valid after delay
                        if (!context.mounted) {
                          debugPrint('Context became unmounted during delay');
                          return;
                        }

                        // Ensure index is valid before navigation
                        if (index < 0 || index >= navBarConfig.items.length) {
                          debugPrint('Invalid navigation index: $index');
                          return;
                        }

                        debugPrint(
                            'About to call onItemSelected with index: $index');

                        // Perform navigation with additional safety
                        navBarConfig.onItemSelected(index);

                        debugPrint('Navigation completed successfully');
                      } catch (e, stackTrace) {
                        debugPrint('Navigation error: $e');
                        debugPrint('Stack trace: $stackTrace');
                        // Try to show a user-friendly message if context is still valid
                        if (context.mounted) {
                          try {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'حدث خطأ في التنقل، يرجى المحاولة مرة أخرى',
                                  style: AppTextStyles.font14Regular.copyWith(
                                    color: Colors.white,
                                  ),
                                ),
                                backgroundColor: theme.colorScheme.error,
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          } catch (snackBarError) {
                            debugPrint(
                                'Could not show snackbar: $snackBarError');
                          }
                        }
                      }
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: _buildItem(
                      item,
                      navBarConfig.selectedIndex == index,
                      context,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}

/// Heartbeat animation widget for navigation icons
class _HeartbeatIcon extends StatefulWidget {
  final bool isSelected;
  final Widget icon;
  final Color primaryColor;
  final bool isDark;

  const _HeartbeatIcon({
    required this.isSelected,
    required this.icon,
    required this.primaryColor,
    required this.isDark,
  });

  @override
  State<_HeartbeatIcon> createState() => _HeartbeatIconState();
}

class _HeartbeatIconState extends State<_HeartbeatIcon>
    with TickerProviderStateMixin {
  late AnimationController _heartbeatController;
  late AnimationController _pulseController;
  late Animation<double> _heartbeatAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Subtle heartbeat animation (scale effect)
    _heartbeatController = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );

    _heartbeatAnimation = Tween<double>(
      begin: 1.0,
      end: 1.03,
    ).animate(CurvedAnimation(
      parent: _heartbeatController,
      curve: Curves.easeInOutSine,
    ));

    // Subtle pulse animation (glow effect)
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOutSine,
    ));

    // Start animations if selected
    if (widget.isSelected) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    _heartbeatController.repeat(reverse: true);
    _pulseController.repeat(reverse: true);
  }

  void _stopAnimations() {
    _heartbeatController.stop();
    _pulseController.stop();
    _heartbeatController.reset();
    _pulseController.reset();
  }

  @override
  void didUpdateWidget(_HeartbeatIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  @override
  void dispose() {
    _heartbeatController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_heartbeatAnimation, _pulseAnimation]),
      builder: (context, child) {
        final scale = widget.isSelected ? _heartbeatAnimation.value : 1.0;
        final pulseValue = widget.isSelected ? _pulseAnimation.value : 0.0;

        return Transform.scale(
          scale: scale,
          child: Container(
            width: 32,
            height: 32,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: widget.isSelected
                  ? widget.primaryColor
                      .withValues(alpha: 0.1 + (0.1 * pulseValue))
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(10),
              boxShadow: widget.isSelected
                  ? [
                      BoxShadow(
                        color: widget.primaryColor
                            .withValues(alpha: 0.1 + (0.1 * pulseValue)),
                        blurRadius: 6 + (8 * pulseValue),
                        spreadRadius: 1 + (3 * pulseValue),
                      ),
                      BoxShadow(
                        color: widget.primaryColor
                            .withValues(alpha: 0.2 * pulseValue),
                        blurRadius: 12 + (6 * pulseValue),
                        spreadRadius: 2 + (2 * pulseValue),
                      ),
                    ]
                  : null,
            ),
            child: AnimatedScale(
              scale: widget.isSelected ? 1.05 : 1.0,
              duration: const Duration(milliseconds: 200),
              child: ColorFiltered(
                colorFilter: ColorFilter.mode(
                  widget.isSelected
                      ? widget.primaryColor
                      : (widget.isDark ? Colors.grey[400]! : Colors.grey[600]!),
                  BlendMode.srcIn,
                ),
                child: SizedBox(
                  width: 26,
                  height: 26,
                  child: FittedBox(
                    fit: BoxFit.contain,
                    alignment: Alignment.center,
                    child: widget.icon,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
